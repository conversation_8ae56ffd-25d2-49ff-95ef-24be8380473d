import React, { useState, useRef, useEffect } from 'react';
import { IconCard } from '@/shared/components/common';
import ModernMenu, { ModernMenuItem } from './ModernMenu';
import { IconName } from '@/shared/components/common/Icon/Icon';

// Define the type for items without requiring the 'id' property
type ModernMenuItemWithoutId = Omit<ModernMenuItem, 'id'> & {
  variant?: 'default' | 'primary' | 'secondary' | 'ghost';
  keepOpen?: boolean;
};

interface ModernMenuTriggerProps {
  items: ModernMenuItemWithoutId[];
  placement?: 'top' | 'right' | 'bottom' | 'left';
  width?: string;
  triggerIcon?: IconName;
  triggerSize?: 'sm' | 'md' | 'lg';
  triggerVariant?: 'default' | 'primary' | 'secondary' | 'ghost';
}

/**
 * Component ModernMenuTrigger hiển thị nút trigger và menu
 */
const ModernMenuTrigger: React.FC<ModernMenuTriggerProps> = ({
  items,
  placement = 'bottom',
  width = '200px',
  triggerIcon = 'more-vertical',
  triggerSize = 'md',
  triggerVariant = 'default',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);

  // Chuyển đổi items để thêm id
  const menuItems: ModernMenuItem[] = items.map((item, index) => ({
    id: `menu-item-${index}`,
    label: item.label,
    icon: item.icon,
    onClick: item.onClick ?? (() => {}),
    disabled: item.disabled ?? false,
    divider: item.divider ?? false,
    keepOpen: item.keepOpen ?? false,
  }));

  // Handle click outside to close the menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (triggerRef.current && !triggerRef.current.contains(event.target as Node) && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleToggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleCloseMenu = () => {
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={triggerRef}>
      <IconCard
        icon={triggerIcon}
        variant={triggerVariant}
        size={triggerSize}
        onClick={handleToggleMenu}
      />
      {isOpen && (
        <ModernMenu
          items={menuItems}
          isOpen={isOpen}
          onClose={handleCloseMenu}
          placement={placement}
          width={width}
          preferRight={true}
          preferTop={true}
          triggerRef={triggerRef}
        />
      )}
    </div>
  );
};

export default ModernMenuTrigger;
